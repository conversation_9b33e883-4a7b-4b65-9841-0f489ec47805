-- Migration: Fix infinite recursion in project_users RLS policies
-- Date: 2025-08-04
-- Description: Disable RLS on project_users table to prevent infinite recursion during project creation

-- ================================
-- DISABLE RLS ON PROJECT_USERS
-- ================================
-- This will prevent infinite recursion issues when creating projects
-- The table doesn't need RLS as access control is handled at the application level
-- through project membership checks

ALTER TABLE public.project_users DISABLE ROW LEVEL SECURITY;

-- Drop any existing policies on project_users table
DROP POLICY IF EXISTS "Users can view their own project memberships" ON public.project_users;
DROP POLICY IF EXISTS "Users can manage their own project memberships" ON public.project_users;
DROP POLICY IF EXISTS "Project admins can manage project memberships" ON public.project_users;
DROP POLICY IF EXISTS "Service role can manage all project memberships" ON public.project_users;

-- Add comment explaining why <PERSON><PERSON> is disabled
COMMENT ON TABLE public.project_users IS 'RLS disabled to prevent infinite recursion. Access control handled at application level through project membership validation.';
